#!/bin/bash

# Advanced Image Resizer for Web
# Supports multiple formats and custom sizes

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Default configuration
INPUT_DIR="input_images"
OUTPUT_DIR="resized_images"
QUALITY=85
PROGRESSIVE=true
STRIP_METADATA=true

# Predefined sizes for different use cases
declare -A SIZES=(
    ["hero"]="1920x1080"
    ["banner"]="1200x400"
    ["gallery"]="800x600"
    ["service"]="600x400"
    ["thumbnail"]="400x300"
    ["card"]="300x200"
    ["avatar"]="150x150"
    ["icon"]="64x64"
)

# Function to show usage
show_usage() {
    echo -e "${BLUE}🖼️  Advanced Web Image Resizer${NC}"
    echo "=================================="
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -i, --input DIR     Input directory (default: input_images)"
    echo "  -o, --output DIR    Output directory (default: resized_images)"
    echo "  -q, --quality NUM   JPEG quality 1-100 (default: 85)"
    echo "  -s, --size WxH      Custom size (e.g., 800x600)"
    echo "  -t, --type TYPE     Predefined type: hero, gallery, service, thumbnail, etc."
    echo "  -f, --format FORMAT Output format: jpg, png, webp (default: keep original)"
    echo "  --no-progressive    Disable progressive JPEG"
    echo "  --keep-metadata     Keep image metadata"
    echo "  -h, --help          Show this help"
    echo ""
    echo "Predefined sizes:"
    for type in "${!SIZES[@]}"; do
        echo "  $type: ${SIZES[$type]}"
    done
    echo ""
    echo "Examples:"
    echo "  $0                                    # Resize all predefined sizes"
    echo "  $0 -t hero -q 90                     # Only hero size, quality 90"
    echo "  $0 -s 1024x768 -f webp               # Custom size, WebP format"
    echo "  $0 -i photos -o web_images           # Custom directories"
}

# Parse command line arguments
CUSTOM_SIZE=""
SELECTED_TYPE=""
OUTPUT_FORMAT=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--input)
            INPUT_DIR="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -q|--quality)
            QUALITY="$2"
            shift 2
            ;;
        -s|--size)
            CUSTOM_SIZE="$2"
            shift 2
            ;;
        -t|--type)
            SELECTED_TYPE="$2"
            shift 2
            ;;
        -f|--format)
            OUTPUT_FORMAT="$2"
            shift 2
            ;;
        --no-progressive)
            PROGRESSIVE=false
            shift
            ;;
        --keep-metadata)
            STRIP_METADATA=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_usage
            exit 1
            ;;
    esac
done

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    if ! command -v convert &> /dev/null; then
        missing_deps+=("imagemagick")
    fi
    
    if ! command -v identify &> /dev/null; then
        missing_deps+=("imagemagick")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${RED}❌ Missing dependencies: ${missing_deps[*]}${NC}"
        echo ""
        echo "Install ImageMagick:"
        echo "  Ubuntu/Debian: sudo apt-get install imagemagick"
        echo "  CentOS/RHEL: sudo yum install ImageMagick"
        echo "  macOS: brew install imagemagick"
        exit 1
    fi
}

# Function to get image info
get_image_info() {
    local image="$1"
    local info=$(identify -format "%wx%h %b" "$image" 2>/dev/null)
    echo "$info"
}

# Function to resize image with options
resize_image_advanced() {
    local input_file="$1"
    local output_file="$2"
    local size="$3"
    local format="$4"
    
    local convert_options=()
    
    # Add resize option
    convert_options+=("-resize" "${size}^")
    convert_options+=("-gravity" "center")
    convert_options+=("-extent" "$size")
    
    # Add quality
    convert_options+=("-quality" "$QUALITY")
    
    # Add progressive JPEG if enabled
    if [ "$PROGRESSIVE" = true ] && [[ "$format" =~ ^jpe?g$ ]]; then
        convert_options+=("-interlace" "Plane")
    fi
    
    # Strip metadata if enabled
    if [ "$STRIP_METADATA" = true ]; then
        convert_options+=("-strip")
    fi
    
    # Add format if specified
    if [ -n "$format" ]; then
        convert_options+=("-format" "$format")
    fi
    
    convert "$input_file" "${convert_options[@]}" "$output_file"
}

# Main processing function
process_images() {
    echo -e "${BLUE}🖼️  Advanced Web Image Resizer${NC}"
    echo "=================================="
    echo ""
    
    # Check dependencies
    check_dependencies
    
    # Create directories
    mkdir -p "$INPUT_DIR"
    
    if [ ! "$(ls -A $INPUT_DIR 2>/dev/null)" ]; then
        echo -e "${YELLOW}⚠️  Input directory '$INPUT_DIR' is empty${NC}"
        echo "Please add images to resize."
        exit 0
    fi
    
    # Determine what to process
    local sizes_to_process=()
    
    if [ -n "$CUSTOM_SIZE" ]; then
        sizes_to_process+=("custom:$CUSTOM_SIZE")
    elif [ -n "$SELECTED_TYPE" ]; then
        if [ -n "${SIZES[$SELECTED_TYPE]}" ]; then
            sizes_to_process+=("$SELECTED_TYPE:${SIZES[$SELECTED_TYPE]}")
        else
            echo -e "${RED}❌ Unknown type: $SELECTED_TYPE${NC}"
            exit 1
        fi
    else
        for type in "${!SIZES[@]}"; do
            sizes_to_process+=("$type:${SIZES[$type]}")
        done
    fi
    
    echo -e "${YELLOW}📋 Configuration:${NC}"
    echo "   Input: $INPUT_DIR"
    echo "   Output: $OUTPUT_DIR"
    echo "   Quality: $QUALITY%"
    echo "   Progressive: $PROGRESSIVE"
    echo "   Strip metadata: $STRIP_METADATA"
    echo "   Output format: ${OUTPUT_FORMAT:-'keep original'}"
    echo ""
    
    # Process each size
    for size_info in "${sizes_to_process[@]}"; do
        IFS=':' read -r type size <<< "$size_info"
        
        echo -e "${CYAN}📐 Processing size: $type ($size)${NC}"
        
        # Create output directory for this size
        mkdir -p "$OUTPUT_DIR/$type"
        
        local processed=0
        
        for image in "$INPUT_DIR"/*.{jpg,jpeg,png,JPG,JPEG,PNG,webp,WEBP} 2>/dev/null; do
            [ -f "$image" ] || continue
            
            local filename=$(basename "$image")
            local name="${filename%.*}"
            local original_ext="${filename##*.}"
            local output_ext="${OUTPUT_FORMAT:-$original_ext}"
            
            local output_file="$OUTPUT_DIR/$type/${name}_${type}.${output_ext}"
            
            echo -e "   ${GREEN}→${NC} $filename"
            
            # Get original image info
            local original_info=$(get_image_info "$image")
            echo -e "     Original: $original_info"
            
            # Resize image
            resize_image_advanced "$image" "$output_file" "$size" "$output_ext"
            
            # Get new image info
            local new_info=$(get_image_info "$output_file")
            echo -e "     Resized:  $new_info"
            
            processed=$((processed + 1))
        done
        
        echo -e "   ${GREEN}✅ Processed $processed images${NC}"
        echo ""
    done
    
    echo -e "${GREEN}🎉 All done!${NC}"
    echo ""
    echo -e "${YELLOW}📁 Output structure:${NC}"
    find "$OUTPUT_DIR" -type f -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.webp" | head -10
    if [ $(find "$OUTPUT_DIR" -type f | wc -l) -gt 10 ]; then
        echo "   ... and more"
    fi
}

# Run main function
process_images
