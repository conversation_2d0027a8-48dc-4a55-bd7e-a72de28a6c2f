#!/bin/bash

# Setup script for image resizing tools
# Installs required dependencies

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🛠️  Setup Image Resizing Tools${NC}"
echo "=================================="

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    if command -v apt-get &> /dev/null; then
        # Debian/Ubuntu
        echo -e "${YELLOW}📦 Installing ImageMagick (Ubuntu/Debian)...${NC}"
        sudo apt-get update
        sudo apt-get install -y imagemagick
        
        echo -e "${YELLOW}🐍 Installing Python3 and pip...${NC}"
        sudo apt-get install -y python3 python3-pip
        
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        echo -e "${YELLOW}📦 Installing ImageMagick (CentOS/RHEL)...${NC}"
        sudo yum install -y ImageMagick
        
        echo -e "${YELLOW}🐍 Installing Python3 and pip...${NC}"
        sudo yum install -y python3 python3-pip
        
    elif command -v dnf &> /dev/null; then
        # Fedora
        echo -e "${YELLOW}📦 Installing ImageMagick (Fedora)...${NC}"
        sudo dnf install -y ImageMagick
        
        echo -e "${YELLOW}🐍 Installing Python3 and pip...${NC}"
        sudo dnf install -y python3 python3-pip
    fi
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    if command -v brew &> /dev/null; then
        echo -e "${YELLOW}📦 Installing ImageMagick (macOS)...${NC}"
        brew install imagemagick
        
        echo -e "${YELLOW}🐍 Installing Python3...${NC}"
        brew install python3
    else
        echo -e "${RED}❌ Homebrew not found. Please install Homebrew first:${NC}"
        echo "/bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
fi

# Install Python packages
echo -e "${YELLOW}🐍 Installing Pillow...${NC}"
pip3 install Pillow

# Make scripts executable
echo -e "${YELLOW}🔧 Making scripts executable...${NC}"
chmod +x resize-images.sh
chmod +x resize-images-advanced.sh
chmod +x resize_images.py

# Test installations
echo -e "${YELLOW}🧪 Testing installations...${NC}"

if command -v convert &> /dev/null; then
    echo -e "${GREEN}✅ ImageMagick installed successfully${NC}"
    convert -version | head -1
else
    echo -e "${RED}❌ ImageMagick installation failed${NC}"
fi

if python3 -c "import PIL; print('Pillow version:', PIL.__version__)" 2>/dev/null; then
    echo -e "${GREEN}✅ Pillow installed successfully${NC}"
else
    echo -e "${RED}❌ Pillow installation failed${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Setup complete!${NC}"
echo ""
echo -e "${YELLOW}📖 Usage:${NC}"
echo ""
echo "1. Basic resize (Bash script):"
echo "   ./resize-images.sh"
echo ""
echo "2. Advanced resize (Bash script):"
echo "   ./resize-images-advanced.sh --help"
echo ""
echo "3. Python script:"
echo "   python3 resize_images.py --help"
echo ""
echo -e "${BLUE}💡 Quick start:${NC}"
echo "1. Create 'input_images' folder"
echo "2. Copy your images to 'input_images'"
echo "3. Run: ./resize-images.sh"
echo "4. Check 'resized_images' folder for results"
