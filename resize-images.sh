#!/bin/bash

# Script resize ảnh cho web
# Tự động resize ảnh từ size lớn thành size phù hợp cho web

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
INPUT_DIR="input_images"
OUTPUT_DIR="resized_images"
QUALITY=85

# Web-optimized sizes
HERO_WIDTH=1920
HERO_HEIGHT=1080
GALLERY_WIDTH=800
GALLERY_HEIGHT=600
THUMBNAIL_WIDTH=400
THUMBNAIL_HEIGHT=300
SERVICE_WIDTH=600
SERVICE_HEIGHT=400

echo -e "${BLUE}🖼️  Web Image Resizer${NC}"
echo "=================================="

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo -e "${RED}❌ ImageMagick không được cài đặt${NC}"
    echo "Cài đặt ImageMagick:"
    echo "Ubuntu/Debian: sudo apt-get install imagemagick"
    echo "CentOS/RHEL: sudo yum install ImageMagick"
    echo "macOS: brew install imagemagick"
    exit 1
fi

# Create directories
mkdir -p "$INPUT_DIR"
mkdir -p "$OUTPUT_DIR"/{hero,gallery,thumbnails,services,original}

echo -e "${YELLOW}📁 Thư mục được tạo:${NC}"
echo "   Input: $INPUT_DIR/"
echo "   Output: $OUTPUT_DIR/"

# Check if input directory has images
if [ ! "$(ls -A $INPUT_DIR 2>/dev/null)" ]; then
    echo -e "${YELLOW}⚠️  Thư mục $INPUT_DIR trống${NC}"
    echo "Hãy copy ảnh cần resize vào thư mục $INPUT_DIR/"
    echo ""
    echo "Ví dụ:"
    echo "  cp /path/to/your/images/* $INPUT_DIR/"
    echo "  hoặc"
    echo "  mv /path/to/your/images/* $INPUT_DIR/"
    exit 0
fi

# Function to resize image
resize_image() {
    local input_file="$1"
    local output_file="$2"
    local width="$3"
    local height="$4"
    local description="$5"
    
    echo -e "   ${BLUE}→${NC} $description: ${width}x${height}"
    
    # Resize with aspect ratio maintained, crop if needed
    convert "$input_file" \
        -resize "${width}x${height}^" \
        -gravity center \
        -extent "${width}x${height}" \
        -quality $QUALITY \
        -strip \
        "$output_file"
}

# Function to optimize without resizing
optimize_image() {
    local input_file="$1"
    local output_file="$2"
    
    convert "$input_file" \
        -quality $QUALITY \
        -strip \
        "$output_file"
}

# Process images
echo -e "${YELLOW}🔄 Bắt đầu resize ảnh...${NC}"
echo ""

total_files=0
processed_files=0

for image in "$INPUT_DIR"/*.{jpg,jpeg,png,JPG,JPEG,PNG} 2>/dev/null; do
    [ -f "$image" ] || continue
    
    total_files=$((total_files + 1))
    filename=$(basename "$image")
    name="${filename%.*}"
    ext="${filename##*.}"
    
    echo -e "${GREEN}📸 Processing: $filename${NC}"
    
    # Hero size (for homepage background)
    resize_image "$image" "$OUTPUT_DIR/hero/${name}_hero.${ext}" $HERO_WIDTH $HERO_HEIGHT "Hero Background"
    
    # Gallery size (for gallery display)
    resize_image "$image" "$OUTPUT_DIR/gallery/${name}_gallery.${ext}" $GALLERY_WIDTH $GALLERY_HEIGHT "Gallery Display"
    
    # Thumbnail size (for previews)
    resize_image "$image" "$OUTPUT_DIR/thumbnails/${name}_thumb.${ext}" $THUMBNAIL_WIDTH $THUMBNAIL_HEIGHT "Thumbnail"
    
    # Service size (for service images)
    resize_image "$image" "$OUTPUT_DIR/services/${name}_service.${ext}" $SERVICE_WIDTH $SERVICE_HEIGHT "Service Image"
    
    # Original optimized (compress without resize)
    echo -e "   ${BLUE}→${NC} Original Optimized"
    optimize_image "$image" "$OUTPUT_DIR/original/${name}_optimized.${ext}"
    
    processed_files=$((processed_files + 1))
    echo ""
done

echo -e "${GREEN}✅ Hoàn thành!${NC}"
echo ""
echo -e "${YELLOW}📊 Thống kê:${NC}"
echo "   Tổng số file: $total_files"
echo "   Đã xử lý: $processed_files"
echo "   Chất lượng: $QUALITY%"
echo ""

echo -e "${YELLOW}📁 Kết quả trong thư mục $OUTPUT_DIR/:${NC}"
echo "   hero/        - Ảnh nền hero (1920x1080)"
echo "   gallery/     - Ảnh gallery (800x600)"
echo "   thumbnails/  - Ảnh thumbnail (400x300)"
echo "   services/    - Ảnh dịch vụ (600x400)"
echo "   original/    - Ảnh gốc đã tối ưu"
echo ""

# Show file sizes
if [ $processed_files -gt 0 ]; then
    echo -e "${YELLOW}💾 Kích thước file:${NC}"
    
    original_size=$(du -sh "$INPUT_DIR" 2>/dev/null | cut -f1)
    output_size=$(du -sh "$OUTPUT_DIR" 2>/dev/null | cut -f1)
    
    echo "   Input:  $original_size"
    echo "   Output: $output_size"
    echo ""
    
    echo -e "${GREEN}🎯 Sử dụng:${NC}"
    echo "1. Copy ảnh phù hợp vào thư mục uploads của website"
    echo "2. Hero images: Dùng cho background homepage"
    echo "3. Gallery images: Dùng cho gallery section"
    echo "4. Thumbnails: Dùng cho preview và cards"
    echo "5. Service images: Dùng cho service pages"
    echo ""
    
    echo -e "${BLUE}💡 Tips:${NC}"
    echo "• Hero images tốt nhất cho background"
    echo "• Gallery images cân bằng chất lượng và tốc độ"
    echo "• Thumbnails load nhanh cho mobile"
    echo "• Service images phù hợp cho content"
fi
