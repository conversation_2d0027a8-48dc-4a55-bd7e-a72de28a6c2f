#!/usr/bin/env python3
"""
Simple Image Resizer for Web
Resize images to web-optimized sizes using Pillow
"""

import os
import sys
from PIL import Image, ImageOps
import argparse
from pathlib import Path

# Web-optimized sizes
WEB_SIZES = {
    'hero': (1920, 1080),
    'banner': (1200, 400),
    'gallery': (800, 600),
    'service': (600, 400),
    'thumbnail': (400, 300),
    'card': (300, 200),
    'avatar': (150, 150),
    'icon': (64, 64)
}

def check_pillow():
    """Check if Pillow is installed"""
    try:
        import PIL
        return True
    except ImportError:
        print("❌ Pillow không được cài đặt")
        print("Cài đặt: pip install Pillow")
        return False

def resize_image(input_path, output_path, size, quality=85):
    """Resize image to specified size"""
    try:
        with Image.open(input_path) as img:
            # Convert to RGB if necessary (for JPEG output)
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # Resize with aspect ratio maintained, crop if needed
            img_resized = ImageOps.fit(img, size, Image.Resampling.LANCZOS)
            
            # Save with optimization
            img_resized.save(
                output_path,
                optimize=True,
                quality=quality,
                progressive=True if output_path.lower().endswith(('.jpg', '.jpeg')) else False
            )
            
            return True
    except Exception as e:
        print(f"❌ Lỗi khi resize {input_path}: {e}")
        return False

def get_image_files(directory):
    """Get all image files from directory"""
    image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff'}
    image_files = []
    
    for file_path in Path(directory).iterdir():
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_files.append(file_path)
    
    return sorted(image_files)

def format_size(size_bytes):
    """Format file size in human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def main():
    parser = argparse.ArgumentParser(description='Resize images for web')
    parser.add_argument('-i', '--input', default='input_images', 
                       help='Input directory (default: input_images)')
    parser.add_argument('-o', '--output', default='resized_images',
                       help='Output directory (default: resized_images)')
    parser.add_argument('-q', '--quality', type=int, default=85,
                       help='JPEG quality 1-100 (default: 85)')
    parser.add_argument('-t', '--type', choices=list(WEB_SIZES.keys()),
                       help='Resize to specific type only')
    parser.add_argument('-s', '--size', 
                       help='Custom size as WIDTHxHEIGHT (e.g., 800x600)')
    parser.add_argument('--list-sizes', action='store_true',
                       help='List available predefined sizes')
    
    args = parser.parse_args()
    
    # Check if Pillow is available
    if not check_pillow():
        sys.exit(1)
    
    # List sizes if requested
    if args.list_sizes:
        print("📐 Available predefined sizes:")
        for name, (width, height) in WEB_SIZES.items():
            print(f"   {name}: {width}x{height}")
        sys.exit(0)
    
    # Create directories
    input_dir = Path(args.input)
    output_dir = Path(args.output)
    
    if not input_dir.exists():
        input_dir.mkdir(parents=True)
        print(f"📁 Created input directory: {input_dir}")
        print("Please add images to resize.")
        sys.exit(0)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Get image files
    image_files = get_image_files(input_dir)
    
    if not image_files:
        print(f"⚠️  No images found in {input_dir}")
        sys.exit(0)
    
    print(f"🖼️  Found {len(image_files)} images to process")
    print(f"📁 Input: {input_dir}")
    print(f"📁 Output: {output_dir}")
    print(f"🎯 Quality: {args.quality}%")
    print()
    
    # Determine sizes to process
    sizes_to_process = {}
    
    if args.size:
        # Custom size
        try:
            width, height = map(int, args.size.split('x'))
            sizes_to_process['custom'] = (width, height)
        except ValueError:
            print("❌ Invalid size format. Use WIDTHxHEIGHT (e.g., 800x600)")
            sys.exit(1)
    elif args.type:
        # Specific type
        sizes_to_process[args.type] = WEB_SIZES[args.type]
    else:
        # All predefined sizes
        sizes_to_process = WEB_SIZES.copy()
    
    # Process images
    total_processed = 0
    total_original_size = 0
    total_output_size = 0
    
    for size_name, (width, height) in sizes_to_process.items():
        print(f"📐 Processing {size_name} ({width}x{height})...")
        
        # Create size-specific output directory
        size_output_dir = output_dir / size_name
        size_output_dir.mkdir(exist_ok=True)
        
        processed_count = 0
        
        for image_file in image_files:
            # Get original file size
            original_size = image_file.stat().st_size
            total_original_size += original_size
            
            # Generate output filename
            output_filename = f"{image_file.stem}_{size_name}{image_file.suffix}"
            output_path = size_output_dir / output_filename
            
            print(f"   → {image_file.name}")
            
            # Resize image
            if resize_image(image_file, output_path, (width, height), args.quality):
                # Get output file size
                if output_path.exists():
                    output_size = output_path.stat().st_size
                    total_output_size += output_size
                    
                    # Show size comparison
                    compression_ratio = (1 - output_size / original_size) * 100
                    print(f"     {format_size(original_size)} → {format_size(output_size)} "
                          f"({compression_ratio:.1f}% smaller)")
                
                processed_count += 1
                total_processed += 1
        
        print(f"   ✅ Processed {processed_count} images")
        print()
    
    # Summary
    print("🎉 Processing complete!")
    print(f"📊 Total processed: {total_processed} images")
    print(f"💾 Original size: {format_size(total_original_size)}")
    print(f"💾 Output size: {format_size(total_output_size)}")
    
    if total_original_size > 0:
        total_compression = (1 - total_output_size / total_original_size) * 100
        print(f"📉 Total compression: {total_compression:.1f}%")
    
    print()
    print("📁 Output structure:")
    for size_name in sizes_to_process.keys():
        size_dir = output_dir / size_name
        if size_dir.exists():
            file_count = len(list(size_dir.glob('*')))
            print(f"   {size_name}/  ({file_count} files)")

if __name__ == '__main__':
    main()
