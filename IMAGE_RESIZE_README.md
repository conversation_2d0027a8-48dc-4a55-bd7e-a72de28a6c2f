# 🖼️ Web Image Resizer Tools

Bộ công cụ đơn giản để resize ảnh từ size lớn thành size phù hợp cho website.

## 📦 Cài đặt

### Tự động (Khuyến nghị)
```bash
chmod +x setup-image-tools.sh
./setup-image-tools.sh
```

### Thủ công

**Ubuntu/Debian:**
```bash
sudo apt-get install imagemagick python3 python3-pip
pip3 install Pillow
```

**CentOS/RHEL:**
```bash
sudo yum install ImageMagick python3 python3-pip
pip3 install Pillow
```

**macOS:**
```bash
brew install imagemagick python3
pip3 install Pillow
```

## 🚀 Sử dụng

### 1. Script Bash Đơn Giản (`resize-images.sh`)

**Cách dùng:**
```bash
# Tạo thư mục và copy ảnh
mkdir input_images
cp /path/to/your/images/* input_images/

# Chạy script
./resize-images.sh
```

**K<PERSON>t quả:**
- `resized_images/hero/` - Ảnh nền (1920x1080)
- `resized_images/gallery/` - Ảnh gallery (800x600)
- `resized_images/thumbnails/` - Ảnh thumbnail (400x300)
- `resized_images/services/` - Ảnh dịch vụ (600x400)
- `resized_images/original/` - Ảnh gốc đã tối ưu

### 2. Script Bash Nâng Cao (`resize-images-advanced.sh`)

**Tùy chọn cơ bản:**
```bash
# Resize tất cả size
./resize-images-advanced.sh

# Chỉ resize hero size
./resize-images-advanced.sh -t hero

# Custom size
./resize-images-advanced.sh -s 1024x768

# Chất lượng cao
./resize-images-advanced.sh -q 95

# Xuất WebP format
./resize-images-advanced.sh -f webp
```

**Tùy chọn nâng cao:**
```bash
# Custom thư mục
./resize-images-advanced.sh -i photos -o web_images

# Giữ metadata
./resize-images-advanced.sh --keep-metadata

# Tắt progressive JPEG
./resize-images-advanced.sh --no-progressive
```

### 3. Script Python (`resize_images.py`)

**Cách dùng:**
```bash
# Resize tất cả size
python3 resize_images.py

# Chỉ resize gallery
python3 resize_images.py -t gallery

# Custom size
python3 resize_images.py -s 800x600

# Xem các size có sẵn
python3 resize_images.py --list-sizes
```

## 📐 Kích Thước Được Định Sẵn

| Loại | Kích Thước | Mục Đích |
|------|------------|----------|
| hero | 1920x1080 | Ảnh nền homepage |
| banner | 1200x400 | Banner sections |
| gallery | 800x600 | Gallery display |
| service | 600x400 | Service images |
| thumbnail | 400x300 | Preview images |
| card | 300x200 | Card components |
| avatar | 150x150 | User avatars |
| icon | 64x64 | Small icons |

## 🎯 Ví Dụ Thực Tế

### Resize ảnh cho Phong Nha Valley website:

```bash
# 1. Copy ảnh từ camera/phone
cp ~/Downloads/photos/* input_images/

# 2. Resize cho website
./resize-images.sh

# 3. Copy ảnh đã resize vào website
cp resized_images/hero/* frontend/public/uploads/
cp resized_images/gallery/* frontend/public/uploads/
cp resized_images/services/* frontend/public/uploads/
```

### Resize ảnh dịch vụ cụ thể:

```bash
# Chỉ resize cho service images
./resize-images-advanced.sh -t service -q 90

# Copy vào thư mục uploads
cp resized_images/service/* frontend/public/uploads/
```

## 📊 Tối Ưu Hóa

### Chất lượng khuyến nghị:
- **Hero images**: 85-90% (cân bằng chất lượng và tốc độ)
- **Gallery**: 80-85% (tốc độ load nhanh)
- **Thumbnails**: 75-80% (ưu tiên tốc độ)
- **Services**: 85% (chất lượng tốt)

### Format khuyến nghị:
- **JPEG**: Cho ảnh có nhiều màu sắc
- **PNG**: Cho ảnh có transparency
- **WebP**: Cho tối ưu tốc độ (modern browsers)

## 🔧 Troubleshooting

### Lỗi "convert: command not found"
```bash
# Ubuntu/Debian
sudo apt-get install imagemagick

# macOS
brew install imagemagick
```

### Lỗi "No module named 'PIL'"
```bash
pip3 install Pillow
```

### Lỗi permission denied
```bash
chmod +x resize-images.sh
chmod +x resize-images-advanced.sh
```

## 💡 Tips

1. **Backup ảnh gốc** trước khi resize
2. **Test với vài ảnh** trước khi resize hàng loạt
3. **Kiểm tra kết quả** trên website sau khi upload
4. **Sử dụng WebP** cho performance tốt hơn
5. **Compress thêm** với tools online nếu cần

## 📱 Mobile Optimization

Để tối ưu cho mobile, sử dụng:
```bash
# Tạo ảnh nhỏ hơn cho mobile
./resize-images-advanced.sh -s 600x400 -q 80 -f webp
```

## 🌐 Integration với Website

Sau khi resize, copy ảnh vào website:
```bash
# Copy vào uploads directory
cp resized_images/hero/* frontend/public/uploads/
cp resized_images/gallery/* frontend/public/uploads/
cp resized_images/services/* frontend/public/uploads/

# Deploy lên server
./manage-fast.sh deploy
```
